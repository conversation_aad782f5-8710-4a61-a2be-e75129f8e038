# Role Custom WordPress Eklentisi

## A<PERSON><PERSON>klama

Role Custom, WordPress için geliştirilmiş özel bir rol yönetimi eklentisidir. Bu eklenti "Superole" adında yeni bir kullanıcı rolü oluşturur ve bu rol için özel yetkilendirmeler sağlar. Superole rolündeki kullanıcılar için Tutor LMS menülerini kısıtlar, WooCommerce'e tam erişim sağlar ve Pazarlama menüsünü gizler.

## Özellikler

### 1. Superole Rolü
- Eklenti etkinleştirildiğinde otomatik olarak "Superole" rolü oluşturulur
- Eklenti devre dışı bırakıldığında rol güvenli bir şekilde kaldırılır
- Bu role sahip kullanıcılar otomatik olarak "subscriber" rolüne dönüştürülür

### 2. <PERSON><PERSON>MS Tam Instructor Yetkileri ⭐ YENİ!
Superole rolündeki kullanıcılar artık **Tutor Instructor rolündeki tüm yetkilere** sahiptir:

**🔑 Tutor LMS Yetkileri (Instructor Rolünden):**
- **Temel WordPress Yetkileri**: `edit_posts`, `read`, `upload_files`
- **Yönetim Yetkileri**: `manage_tutor`, `manage_tutor_instructor`, `tutor_instructor`
- **Kurs Yetkileri**: Kurs oluşturma, düzenleme, silme, yayınlama (`edit_tutor_course`, `publish_tutor_courses`)
- **Ders Yetkileri**: Ders oluşturma, düzenleme, silme, yayınlama (`edit_tutor_lesson`, `publish_tutor_lessons`)
- **Quiz Yetkileri**: Quiz oluşturma, düzenleme, silme, yayınlama (`edit_tutor_quiz`, `publish_tutor_quizzes`)
- **Soru Yetkileri**: Soru oluşturma, düzenleme, silme, yayınlama (`edit_tutor_question`, `publish_tutor_questions`)

**📋 Erişilebilir Tutor LMS Menüleri:**
- **Kurslar** (Ana sayfa - tam kurs yönetimi)
- **Öğrenciler** (Öğrenci yönetimi)
- **Duyurular** (Kurs duyuruları)
- **Q&A** (Soru-cevap yönetimi)
- **Sınav Denemeleri** (Quiz sonuçları)
- **Tüm diğer instructor menüleri** (Araçlar ve Ayarlar hariç)

**🚫 Gizlenen Admin Menüleri:**
- Tools (Araçlar)
- Settings (Ayarlar)
- **Para Çekme Talepleri** (Withdrawals) ← **YENİ!**
- Upgrade to Pro

### 3. WooCommerce Özelleştirilmiş Menü Yapısı
Superole rolündeki kullanıcılar için WooCommerce menü yapısı tamamen yeniden düzenlenmiştir:

**Görünür Ana Menüler:**
- **Tutor LMS** (Ana sayfa - otomatik yönlendirme) ← **YENİ!**
- **Ortamlar**
- **Ürünler** (Tüm ürün yönetimi)
- **Siparişler** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-orders`)
- **Müşteriler** ← **YENİ ANA MENÜ!** (`users.php?role=customer`)
- **Raporlar** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-reports`)
- **Kuponlar** ← **YENİ ANA MENÜ!** (`edit.php?post_type=shop_coupon`)
- **Raporlar** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-reports`)
- **Analiz** (WooCommerce Analytics - filtrelenmiş) ← **YENİ ÖZELLİK!**

**Tamamen Gizlenen Menüler:**
- **Dashboard** (Başlangıç - WordPress ana sekmesi)
- **Kullanıcılar** (WordPress ana sekmesi) ← **YENİ!**
- **WooCommerce** (Ana sekme tamamen gizli)
- **Ödemeler** (Ana sekme tamamen gizli)
- **Pazarlama** (Ana sekme tamamen gizli) ← **YENİ!**
- **Araçlar** (WordPress ana sekmesi)
- **Ayarlar** (WooCommerce alt menüsünde)
- **Durum** (WooCommerce alt menüsünde)
- **Genişletme Paketleri** (WooCommerce alt menüsünde)
- **Kuponlar** (Pazarlama alt menüsünden - artık ana menü)

### 5. WooCommerce Analytics ve Raporlama Filtreleme ⭐ YENİ!
Superole rolündeki kullanıcılar için WooCommerce Analytics ve Raporlama bölümlerinde **sadece kendi ürünleriyle ilgili veriler** gösterilir:

**📊 Filtrelenen Analytics Bölümleri:**
- **Siparişler Analizi**: Sadece kullanıcının ürünlerini içeren siparişler
- **Gelir Raporları**: Sadece kullanıcının ürünlerinden elde edilen gelir
- **Ürün Performansı**: Sadece kullanıcının ürünlerinin performans verileri
- **Satış İstatistikleri**: Kullanıcının ürünlerine özel satış verileri
- **Müşteri Analizi**: Sadece kullanıcının ürünlerini satın alan müşteriler
- **Kupon Raporları**: Sadece kullanıcının oluşturduğu kuponlar
- **İndirme Raporları**: Sadece kullanıcının ürünlerinin indirme verileri
- **Vergi Raporları**: Kullanıcının ürünlerinden kaynaklanan vergi verileri

**🎯 Filtreleme Özellikleri:**
- **Otomatik Veri Filtreleme**: Tüm Analytics API sorguları otomatik filtrelenir
- **Dashboard Widget'ları**: Ana sayfa widget'ları kullanıcının verilerine göre güncellenir
- **Performance Indicators**: Performans göstergeleri sadece kullanıcının verilerini yansıtır
- **Leaderboards**: Lider tabloları sadece kullanıcının ürünlerini içerir
- **Real-time Filtreleme**: JavaScript ile dinamik sayfa filtreleme
- **REST API Filtreleme**: WooCommerce REST API endpoint'leri de filtrelenir

**🔧 Teknik Uygulama:**
- **Analytics Query Args**: 14+ farklı analytics hook'u filtrelenir
- **Select Query Results**: Sonuç verileri de ek olarak filtrelenir
- **Classic Reports**: Eski WooCommerce raporları da desteklenir
- **HPOS Uyumluluğu**: Yeni sipariş sistemi ile tam uyumlu
- **Performance Optimized**: Minimum veritabanı sorgusu ile maksimum performans

### 6. Otomatik Ana Sayfa Yönlendirmesi ⭐ YENİ!
Superole rolündeki kullanıcılar WordPress admin paneline giriş yaptığında:
- **Dashboard** (Başlangıç) sayfası yerine
- **Tutor LMS Kurslar** sayfasına otomatik yönlendirilir
- Bu sayede kullanıcılar direkt olarak kurs yönetimi ile başlar

## Gereksinimler

- WordPress 5.0 veya üzeri
- PHP 7.4 veya üzeri
- Tutor LMS eklentisi (menü kısıtlamaları için)
- WooCommerce eklentisi (e-ticaret özellikleri için)

## Kurulum

1. `role-custom` klasörünü WordPress'in `/wp-content/plugins/` dizinine yükleyin
2. WordPress admin panelinde "Eklentiler" bölümüne gidin
3. "Role Custom" eklentisini bulun ve "Etkinleştir" butonuna tıklayın
4. Eklenti etkinleştirildikten sonra "Superole" rolü otomatik olarak oluşturulacaktır

## Kullanım

### Superole Rolü Atama
1. WordPress admin panelinde "Kullanıcılar" > "Tüm Kullanıcılar" bölümüne gidin
2. Düzenlemek istediğiniz kullanıcıyı seçin
3. "Rol" dropdown menüsünden "Superole" seçin
4. "Kullanıcıyı Güncelle" butonuna tıklayın

### Yetkilerin Kontrolü
Superole rolüne sahip bir kullanıcı ile giriş yaparak:
- Tutor LMS menülerinin kısıtlandığını
- WooCommerce'e tam erişim olduğunu
- Sadece izin verilen menülerin görüntülendiğini kontrol edebilirsiniz

## Teknik Detaylar

### Hook'lar ve Filtreler
**Temel Sistem Hook'ları:**
- `admin_menu` (priority: 999) - Tutor LMS menü kısıtlamaları
- `admin_init` - WooCommerce yetkilerinin ayarlanması
- `admin_notices` - Bildirim mesajları
- `admin_head` - CSS ile menü gizleme
- `user_has_cap` - Kullanıcı yetkilerinin filtrelenmesi

**WooCommerce Veri Filtreleme Hook'ları:**
- `pre_get_posts` - Ürün ve kupon listesi filtreleme
- `posts_where` - Sipariş sorguları filtreleme
- `woocommerce_orders_table_query_clauses` - HPOS sipariş filtreleme
- `wc_get_orders_args` - WooCommerce sipariş API filtreleme

**Analytics Filtreleme Hook'ları (14+ hook):**
- `woocommerce_analytics_orders_query_args` - Sipariş analizi
- `woocommerce_analytics_orders_stats_query_args` - Sipariş istatistikleri
- `woocommerce_analytics_revenue_query_args` - Gelir analizi
- `woocommerce_analytics_products_query_args` - Ürün analizi
- `woocommerce_analytics_products_stats_query_args` - Ürün istatistikleri
- `woocommerce_analytics_variations_query_args` - Varyasyon analizi
- `woocommerce_analytics_coupons_query_args` - Kupon analizi
- `woocommerce_analytics_downloads_query_args` - İndirme analizi
- `woocommerce_analytics_customers_query_args` - Müşteri analizi
- `woocommerce_analytics_taxes_query_args` - Vergi analizi
- Ve daha fazlası...

**Reports Filtreleme Hook'ları:**
- `woocommerce_reports_get_order_report_data_args` - Klasik raporlar
- `woocommerce_dashboard_status_widget_sales_query` - Dashboard widget'ları
- `woocommerce_analytics_performance_indicators_query_args` - Performans göstergeleri

### Güvenlik
- Doğrudan dosya erişimi engellendi
- Tüm kullanıcı girişleri sanitize edildi
- WordPress nonce sistemi kullanıldı
- Yetki kontrolleri her adımda yapıldı

### Performans
- Minimum veritabanı sorgusu
- Efficient hook kullanımı
- Transient cache sistemi
- Lazy loading uygulandı

## Sorun Giderme

### Eklenti Etkinleştirme Sorunları
- WordPress ve PHP sürümlerini kontrol edin
- Diğer eklentilerle çakışma olup olmadığını kontrol edin
- WordPress debug modunu etkinleştirin

### Menü Kısıtlamaları Çalışmıyor
- Tutor LMS eklentisinin aktif olduğundan emin olun
- Kullanıcının Superole rolüne sahip olduğunu kontrol edin
- Tarayıcı cache'ini temizleyin

### WooCommerce Yetkileri Çalışmıyor
- WooCommerce eklentisinin aktif olduğundan emin olun
- Kullanıcının Superole rolüne sahip olduğunu kontrol edin
- WordPress yetkilerini yeniden yükleyin

## Kaldırma

1. WordPress admin panelinde "Eklentiler" bölümüne gidin
2. "Role Custom" eklentisini bulun ve "Devre Dışı Bırak" butonuna tıklayın
3. Eklenti devre dışı bırakıldığında:
   - Superole rolü otomatik olarak kaldırılır
   - Bu role sahip kullanıcılar "subscriber" rolüne dönüştürülür
   - Eklenti ayarları temizlenir

## Test Sistemi

### Otomatik Test Aracı
Eklenti ile birlikte kapsamlı bir test sistemi gelir:

**Test Erişimi:**
- WordPress Admin > Araçlar > Role Custom Test
- Sadece admin yetkisine sahip kullanıcılar erişebilir

**Test Kategorileri:**
- **Eklenti Yüklenme Testi**: Temel sınıf ve fonksiyon kontrolleri
- **Superole Rolü Testi**: Rol varlığı ve yetki kontrolleri
- **Tutor LMS Entegrasyon**: Eklenti uyumluluğu ve menü erişimi
- **WooCommerce Entegrasyon**: Eklenti uyumluluğu ve yetki kontrolleri
- **Menü Kısıtlamaları**: Hook'ların doğru çalışması
- **Veri Filtreleme**: WooCommerce veri filtreleme sistemi
- **Analytics Filtreleme**: Analytics API filtreleme sistemi ← **YENİ!**

**Test Sonuçları:**
- Detaylı başarı/başarısızlık raporları
- Hata mesajları ve çözüm önerileri
- Performans ve uyumluluk kontrolleri

## Changelog

### 1.1.0 ⭐ YENİ SÜRÜM!
- **WooCommerce Analytics Filtreleme**: Tam Analytics API filtreleme sistemi
- **Raporlama Filtreleme**: Klasik ve yeni raporlama sistemleri filtreleme
- **Dashboard Widget Filtreleme**: Ana sayfa widget'ları filtreleme
- **Performance Indicators**: Performans göstergeleri filtreleme
- **Leaderboards Filtreleme**: Lider tabloları filtreleme
- **JavaScript Filtreleme**: Real-time sayfa filtreleme
- **REST API Filtreleme**: WooCommerce REST API endpoint filtreleme
- **HPOS Tam Uyumluluk**: Yeni sipariş sistemi tam desteği
- **Kapsamlı Test Sistemi**: Analytics filtreleme testleri eklendi
- **14+ Analytics Hook**: Tüm analytics sorguları filtrelenir
- **Performans Optimizasyonu**: Minimum sorgu, maksimum performans

### 1.0.0
- İlk sürüm
- Superole rolü oluşturma/kaldırma sistemi
- Tutor LMS menü kısıtlama sistemi
- WooCommerce tam erişim sistemi
- Admin bildirim sistemi
- Çoklu dil desteği hazırlığı

## Destek

Bu eklenti ile ilgili sorularınız için:
- GitHub Issues bölümünü kullanın
- WordPress.org destek forumlarına başvurun
- Geliştirici ile iletişime geçin

## Lisans

Bu eklenti GPL v2 veya üzeri lisansı altında dağıtılmaktadır.

## Geliştirici

Role Custom Developer
- Website: https://example.com
- Email: <EMAIL>

---

**Not:** Bu eklenti WordPress standartlarına uygun olarak geliştirilmiştir ve sürekli güncellenmektedir.
